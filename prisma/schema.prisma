generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model UserLocation {
  id             String   @id @default(cuid())
  userId         String   @unique
  latitude       Float? // 纬度
  longitude      Float? // 经度
  lastUpdate     DateTime @db.DateTime(0)
  lastOnlineTime DateTime @db.DateTime(0)
  user           User     @relation(fields: [userId], references: [userId])

  @@index([userId], map: "UserLocation_userId_fkey")
}

model User {
  id              String         @id @default(cuid())
  userId          String         @unique
  passwordHash    String?
  nickname        String
  avatar          String?
  avatarURL       String? // 使用COS的URL存储
  lastActiveTime  DateTime?      @db.DateTime(0)
  createdAt       DateTime?      @db.DateTime(0)
  friendships     Friendship[]   @relation("UserFriendships")
  locations       UserLocation[]
  itemCards       ItemCard[] // 用户创建的卡片
  sharingLocation Boolean        @default(true) // 用户可以选择是否共享位置，默认开启
  carbonCoins     Int            @default(0) // 用户的碳币数量
}

model Friendship {
  id        String           @id @default(cuid())
  userId    String
  friendId  String
  status    FriendshipStatus
  createdAt DateTime         @db.DateTime(0)
  updatedAt DateTime         @db.DateTime(0)
  user      User             @relation("UserFriendships", fields: [userId], references: [userId])

  @@index([userId], map: "Friendship_userId_fkey")
}

enum FriendshipStatus {
  pending
  accepted
  rejected
}

model ItemCard {
  id            String   @id @default(cuid())
  tags          Json // 存储标签数组
  description   String   @db.Text
  title         String
  imageFileName String // 本地存储的文件名
  imageURL      String // COS返回的URL，供共享使用
  createdAt     DateTime @default(now()) @db.DateTime(0)
  authorId      String // 存储创建这个卡片的用户id
  location      String // 位置字符串，用于显示（如"北京市朝阳区"）
  latitude      Float? // 纬度
  longitude     Float? // 经度
  remark        String?  @db.Text // 由用户填写的备注信息（可选）
  author        User     @relation(fields: [authorId], references: [userId])

  @@index([authorId], map: "ItemCard_authorId_fkey")
  @@index([createdAt], map: "ItemCard_createdAt_idx")
}

model ItemCardTransfer {
}
