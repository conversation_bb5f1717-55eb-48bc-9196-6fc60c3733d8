import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 创建新的卡片
export async function POST(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const {
      tags,
      description,
      title,
      imageFileName,
      imageURL,
      location,
      latitude,
      longitude,
      remark,
    } = await req.json();
    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    const itemCard = await prisma.itemCard.create({
      data: {
        tags,
        description,
        title,
        imageFileName,
        imageURL,
        location,
        latitude,
        longitude,
        remark,
        authorId: userId,
      },
    });
    return NextResponse.json({
      success: true,
      message: "卡片创建成功",
      data: itemCard,
    });
  } catch (error) {
    console.error("创建卡片失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误." },
      { status: 500 }
    );
  }
}
